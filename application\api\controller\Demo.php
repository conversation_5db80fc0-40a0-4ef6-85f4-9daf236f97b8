<?php

namespace app\api\controller;

use app\common\controller\Api;
use think\Db;
use app\common\logic\Equipment;
use Endroid\QrCode\QrCode;
use MyImages;
/**
 * 示例接口
 */
class Demo extends Api
{

    //如果$noNeedLogin为空表示所有接口都需要登录才能请求
    //如果$noNeedRight为空表示所有接口都需要验证权限才能请求
    //如果接口已经设置无需登录,那也就无需鉴权了
    //
    // 无需登录的接口,*表示全部
    protected $noNeedLogin = '*';
    // 无需鉴权的接口,*表示全部
    protected $noNeedRight = ['test2'];

    /**
     * 测试方法
     *
     * @ApiTitle    (测试名称)
     * @ApiSummary  (测试描述信息)
     * @ApiMethod   (POST)
     * @ApiRoute    (/api/demo/test/id/{id}/name/{name})
     * @ApiHeaders  (name=token, type=string, required=true, description="请求的Token")
     * @ApiParams   (name="id", type="integer", required=true, description="会员ID")
     * @ApiParams   (name="name", type="string", required=true, description="用户名")
     * @ApiParams   (name="data", type="object", sample="{'user_id':'int','user_name':'string','profile':{'email':'string','age':'integer'}}", description="扩展数据")
     * @ApiReturnParams   (name="code", type="integer", required=true, sample="0")
     * @ApiReturnParams   (name="msg", type="string", required=true, sample="返回成功")
     * @ApiReturnParams   (name="data", type="object", sample="{'user_id':'int','user_name':'string','profile':{'email':'string','age':'integer'}}", description="扩展数据返回")
     * @ApiReturn   ({
         'code':'1',
         'msg':'返回成功'
        })
     */

    public function index(){
        phpinfo();
    }
    public function test()
    {
        $this->success('返回成功', $this->request->param());
    }

    /**
     * 无需登录的接口
     *
     */
    public function test1()
    {
        $this->success('返回成功', ['action' => 'test1']);
    }

    /**
     * 需要登录的接口
     *
     */
    public function test2()
    {
        $this->success('返回成功', ['action' => 'test2']);
    }

    /**
     * 需要登录且需要验证有相应组的权限
     *
     */
    public function test3()
    {
        $this->success('返回成功', ['action' => 'test3']);
    }


    /**
     * 测试方法5：从API模块调用Logic发送指令
     *
     * @ApiTitle    (测试5：发送地锁指令)
     * @ApiSummary  (用于测试从API模块调用通用的EquipmentLogic来发送MQTT指令)
     * @ApiMethod   (GET)
     * @ApiRoute    (/api/demo/test5)
     * @ApiParams   (name="sn", type="string", required=true, description="要操作的地锁SN码")
     * @ApiParams   (name="action", type="string", required=true, sample="open 或 close", description="要执行的操作")
     * @ApiReturn   ({
    'code':'1',
    'msg':'指令已发送，请等待设备响应',
    "data": []
    })
     */
    public function test5()
    {
        // --- 1. API控制器职责：获取并验证请求参数 ---
//        $sn = $this->request->request("sn");
//        if (!$sn) {
//            $this->error('参数错误: 缺少 sn');
//        }
//
//        $action = $this->request->request("action");
//        if (!in_array($action, ['open', 'close'])) {
//            $this->error('参数错误: action 必须是 open 或 close');
//        }

        $sn=710001;
        $action='close';

        // 验证设备是否存在于数据库中 (这是一个好习惯)
        $equipmentExists = Db::name('equipment')->where('sn', $sn)->count();
        if (!$equipmentExists) {
            $this->error('操作失败: 数据库中不存在SN为 ' . $sn . ' 的设备');
        }

        // --- 2. API控制器职责：调用逻辑层处理核心业务 ---
        $logic = new Equipment();
        $result = $logic->sendSwitchCommand($sn, $action);

        // --- 3. API控制器职责：根据逻辑层返回的结果，向客户端返回JSON响应 ---
        if ($result['code'] === 1) {
            // Logic层执行成功
            $this->success($result['msg']);
        } else {
            // Logic层执行失败
            $this->error($result['msg']);
        }
    }

    /**
     * 测试方法6：从API模块调用Logic发送查询状态指令
     *
     * @ApiTitle    (测试6：查询地锁状态)
     * @ApiSummary  (用于测试从API模块调用通用的EquipmentLogic来发送查询状态指令)
     * @ApiMethod   (GET)
     * @ApiRoute    (/api/demo/test6)
     * @ApiParams   (name="sn", type="string", required=true, description="要查询的地锁SN码")
     * @ApiReturn   ({
    'code':'1',
    'msg':'查询指令已发送，请等待设备上报状态',
    "data": []
    })
     */
    public function test6()
    {
        // --- 1. API控制器职责：获取并验证请求参数 ---
//        $sn = $this->request->request("sn");
//        if (!$sn) {
//            $this->error('参数错误: 缺少 sn');
//        }
$sn=710001;
        // 验证设备是否存在于数据库中
        $equipmentExists = Db::name('equipment')->where('sn', $sn)->count();
        if (!$equipmentExists) {
            $this->error('操作失败: 数据库中不存在SN为 ' . $sn . ' 的设备');
        }

        // --- 2. API控制器职责：调用逻辑层处理核心业务 ---
        $logic = new Equipment();
        $result = $logic->queryDeviceStatus($sn);

        // --- 3. API控制器职责：根据逻辑层返回的结果，向客户端返回JSON响应 ---
        if ($result['code'] === 1) {
            // Logic层执行成功
            $this->success($result['msg']);
        } else {
            // Logic层执行失败
            $this->error($result['msg']);
        }
    }
    /**
     * 批量生成设备二维码
     *
     * @ApiTitle    (批量生成设备二维码)
     * @ApiSummary  (批量生成710001-710019共19个设备的二维码，底部显示设备编号)
     * @ApiMethod   (GET)
     * @ApiRoute    (/api/demo/batchGenerateQrCodes)
     * @ApiParams   (name="domain", type="string", required=false, description="域名，默认使用当前域名")
     * @ApiReturn   ({
        'code':'1',
        'msg':'批量生成完成',
        'data': {
            'success_count': 19,
            'failed_count': 0,
            'results': []
        }
        })
     */
    public function batchGenerateQrCodes()
    {
        // 获取域名参数，默认使用当前域名
        $domain = $this->request->get('domain', $_SERVER['HTTP_HOST']);

        // 设备编号范围：710001-710019
        $startNum = 710001;
        $endNum = 710019;

        $results = [];
        $successCount = 0;
        $failedCount = 0;

        // 确保batch目录存在
        $batchDir = ROOT_PATH . 'public/uploads/ewm/batch/';
        if (!is_dir($batchDir)) {
            mkdir($batchDir, 0755, true);
        }

        // 循环生成每个设备的二维码
        for ($deviceNum = $startNum; $deviceNum <= $endNum; $deviceNum++) {
            try {
                $result = $this->generateSingleQrCode($deviceNum, $domain);
                if ($result['success']) {
                    $successCount++;
                    $results[] = [
                        'device_no' => $deviceNum,
                        'status' => 'success',
                        'qr_path' => $result['qr_path'],
                        'final_image_path' => $result['final_image_path'],
                        'final_image_url' => $result['final_image_url']
                    ];
                } else {
                    $failedCount++;
                    $results[] = [
                        'device_no' => $deviceNum,
                        'status' => 'failed',
                        'error' => $result['error']
                    ];
                }
            } catch (\Exception $e) {
                $failedCount++;
                $results[] = [
                    'device_no' => $deviceNum,
                    'status' => 'failed',
                    'error' => $e->getMessage()
                ];
            }
        }

        $this->success('批量生成完成', [
            'success_count' => $successCount,
            'failed_count' => $failedCount,
            'total_count' => $endNum - $startNum + 1,
            'results' => $results
        ]);
    }

    /**
     * 生成单个设备的二维码
     *
     * @param int $deviceNum 设备编号
     * @param string $domain 域名
     * @return array
     */
    private function generateSingleQrCode($deviceNum, $domain)
    {
        try {
            // 1. 生成基础二维码
            $qrUrl = 'https://' . $domain . '/kpl/' . $deviceNum;
            $qrCode = new QrCode();
            $qrCode->setText($qrUrl)->setSize(512);

            // 生成唯一的二维码文件名
            $timestamp = time();
            $qrName = 'qr_' . $deviceNum . '_' . $timestamp . '.png';
            $qrPath = ROOT_PATH . 'public/uploads/ewm/' . $qrName;

            // 保存基础二维码
            $qrCode->save($qrPath);
            $qrRelativePath = 'uploads/ewm/' . $qrName;

            // 2. 使用MyImages\Generate创建带背景的完整二维码
            $img = new \MyImages\Generate();

            // 为最终图片生成文件名
            $finalImageName = 'final_qr_' . $deviceNum . '_' . $timestamp . '.png';
            $finalImagePath = ROOT_PATH . 'public/uploads/ewm/batch/' . $finalImageName;

            // 调用createSharePng并提供文件名参数，避免直接输出到浏览器
            $img->createSharePng($qrRelativePath, $deviceNum, '地锁设备', $finalImagePath);

            // 检查文件是否成功生成
            if (file_exists($finalImagePath)) {
                $finalImageRelativePath = 'uploads/ewm/batch/' . $finalImageName;
                return [
                    'success' => true,
                    'qr_path' => $qrRelativePath,
                    'final_image_path' => $finalImageRelativePath,
                    'final_image_url' => 'http://' . $domain . '/' . $finalImageRelativePath
                ];
            } else {
                return [
                    'success' => false,
                    'error' => '最终图片生成失败'
                ];
            }

        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }





}
