<?php

return array (
  0 => 
  array (
    'name' => 'maptype',
    'title' => '默认地图类型',
    'type' => 'radio',
    'content' => 
    array (
      'baidu' => '百度地图',
      'amap' => '高德地图',
      'tencent' => '腾讯地图',
    ),
    'value' => 'tencent',
    'rule' => 'required',
    'msg' => '',
    'tip' => '',
    'ok' => '',
    'extend' => '',
  ),
  1 => 
  array (
    'name' => 'zoom',
    'title' => '默认缩放级别',
    'type' => 'string',
    'content' => 
    array (
    ),
    'value' => '11',
    'rule' => 'required',
    'msg' => '',
    'tip' => '',
    'ok' => '',
    'extend' => '',
  ),
  2 => 
  array (
    'name' => 'lat',
    'title' => '默认Lat',
    'type' => 'string',
    'content' => 
    array (
    ),
    'value' => '39.919990',
    'rule' => 'required',
    'msg' => '',
    'tip' => '',
    'ok' => '',
    'extend' => '',
  ),
  3 => 
  array (
    'name' => 'lng',
    'title' => '默认Lng',
    'type' => 'string',
    'content' => 
    array (
    ),
    'value' => '116.456270',
    'rule' => 'required',
    'msg' => '',
    'tip' => '',
    'ok' => '',
    'extend' => '',
  ),
  4 => 
  array (
    'name' => 'baidukey',
    'title' => '百度地图KEY',
    'type' => 'string',
    'content' => 
    array (
    ),
    'value' => '',
    'rule' => '',
    'msg' => '',
    'tip' => '',
    'ok' => '',
    'extend' => '',
  ),
  5 => 
  array (
    'name' => 'amapkey',
    'title' => '高德地图KEY',
    'type' => 'string',
    'content' => 
    array (
    ),
    'value' => '',
    'rule' => '',
    'msg' => '',
    'tip' => '',
    'ok' => '',
    'extend' => '',
  ),
  6 => 
  array (
    'name' => 'amapsecurityjscode',
    'title' => '高德地图安全密钥',
    'type' => 'string',
    'content' => 
    array (
    ),
    'value' => '',
    'rule' => '',
    'msg' => '',
    'tip' => '',
    'ok' => '',
    'extend' => '',
  ),
  7 => 
  array (
    'name' => 'tencentkey',
    'title' => '腾讯地图KEY',
    'type' => 'string',
    'content' => 
    array (
    ),
    'value' => 'DJBBZ-PFEAQ-6QP5R-2NPHQ-SZ3QQ-QMBIP',
    'rule' => '',
    'msg' => '',
    'tip' => '',
    'ok' => '',
    'extend' => '',
  ),
  8 => 
  array (
    'name' => '__tips__',
    'title' => '温馨提示',
    'type' => '',
    'content' => 
    array (
    ),
    'value' => '请先申请对应地图的Key，配置后再使用',
    'rule' => '',
    'msg' => '',
    'tip' => '',
    'ok' => '',
    'extend' => 'alert-danger-light',
  ),
);
